#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel照片提取器的功能
"""

import os
import sys
import tempfile
from excel_photo_extractor import ExcelPhotoExtractor
from openpyxl import Workbook
from openpyxl.drawing.image import Image as OpenpyxlImage
from PIL import Image, ImageDraw
import io


def create_test_excel_with_images():
    """创建一个包含图片的测试Excel文件"""
    # 创建工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "5月15日钻井记录"
    
    # 添加表头
    headers = ["井号", "日期", "入井照片", "出井照片", "下趟照片", "备注"]
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # 添加数据行
    ws.cell(row=2, column=1, value="井001")
    ws.cell(row=2, column=2, value="5-15")
    ws.cell(row=2, column=6, value="正常作业")
    
    # 创建测试图片
    def create_test_image(text: str, color: str = "red") -> bytes:
        """创建一个简单的测试图片"""
        img = Image.new('RGB', (200, 100), color='white')
        draw = ImageDraw.Draw(img)
        draw.rectangle([10, 10, 190, 90], outline=color, width=2)
        draw.text((50, 40), text, fill=color)
        
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        return img_bytes.getvalue()
    
    # 创建临时图片文件并添加到Excel
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 入井照片
        img1_data = create_test_image("入井作业", "blue")
        img1_path = os.path.join(temp_dir, "img1.png")
        with open(img1_path, 'wb') as f:
            f.write(img1_data)
        
        img1 = OpenpyxlImage(img1_path)
        img1.width = 100
        img1.height = 50
        ws.add_image(img1, 'C2')  # 入井照片列
        
        # 出井照片
        img2_data = create_test_image("出井作业", "green")
        img2_path = os.path.join(temp_dir, "img2.png")
        with open(img2_path, 'wb') as f:
            f.write(img2_data)
        
        img2 = OpenpyxlImage(img2_path)
        img2.width = 100
        img2.height = 50
        ws.add_image(img2, 'D2')  # 出井照片列
        
        # 下趟照片
        img3_data = create_test_image("下趟作业", "orange")
        img3_path = os.path.join(temp_dir, "img3.png")
        with open(img3_path, 'wb') as f:
            f.write(img3_data)
        
        img3 = OpenpyxlImage(img3_path)
        img3.width = 100
        img3.height = 50
        ws.add_image(img3, 'E2')  # 下趟照片列
        
        # 保存Excel文件
        test_file = "test_excel_with_images.xlsx"
        wb.save(test_file)
        print(f"创建测试Excel文件: {test_file}")
        
        return test_file
        
    finally:
        # 清理临时文件
        for file in os.listdir(temp_dir):
            os.remove(os.path.join(temp_dir, file))
        os.rmdir(temp_dir)


def test_image_extraction():
    """测试图片提取功能"""
    print("开始测试图片提取功能...")
    
    # 创建测试Excel文件
    test_file = create_test_excel_with_images()
    
    if not os.path.exists(test_file):
        print("测试Excel文件创建失败")
        return False
    
    try:
        # 创建提取器实例
        extractor = ExcelPhotoExtractor()
        extractor.output_dir = "test_output"
        
        # 确保输出目录存在
        os.makedirs(extractor.output_dir, exist_ok=True)
        
        # 处理测试文件
        print(f"处理测试文件: {test_file}")
        images_count = extractor.process_excel_file(test_file, "2024")
        
        print(f"提取到 {images_count} 张图片")
        
        # 检查输出目录
        if os.path.exists(extractor.output_dir):
            output_files = os.listdir(extractor.output_dir)
            print(f"输出文件: {output_files}")
            
            # 验证文件
            png_files = [f for f in output_files if f.endswith('.png')]
            print(f"PNG文件数量: {len(png_files)}")
            
            for png_file in png_files:
                file_path = os.path.join(extractor.output_dir, png_file)
                file_size = os.path.getsize(file_path)
                print(f"  {png_file}: {file_size} bytes")
        
        return images_count > 0
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)


def test_filename_generation():
    """测试文件名生成功能"""
    print("\n开始测试文件名生成功能...")
    
    extractor = ExcelPhotoExtractor()
    
    # 测试用例
    test_cases = [
        {
            'context': {
                'header': '入井照片',
                'indicator': '入井',
                'date': '05-15',
                'well_name': '井001'
            },
            'year': '2024',
            'sheet_name': '钻井记录'
        },
        {
            'context': {
                'header': '出井照片',
                'indicator': '出井',
                'date': '05-16',
                'well_name': '井002'
            },
            'year': '2024',
            'sheet_name': '作业记录'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        filename = extractor._generate_filename(
            test_case['context'],
            test_case['year'],
            test_case['sheet_name']
        )
        print(f"测试用例 {i}: {filename}")
    
    return True


def main():
    """主测试函数"""
    print("Excel照片提取器测试程序")
    print("=" * 50)
    
    # 检查依赖
    try:
        import openpyxl
        import PIL
        import pandas as pd
        print("✓ 所有依赖库已安装")
    except ImportError as e:
        print(f"✗ 缺少依赖库: {e}")
        return False
    
    # 测试文件名生成
    if not test_filename_generation():
        print("✗ 文件名生成测试失败")
        return False
    print("✓ 文件名生成测试通过")
    
    # 测试图片提取
    if not test_image_extraction():
        print("✗ 图片提取测试失败")
        return False
    print("✓ 图片提取测试通过")
    
    print("\n" + "=" * 50)
    print("所有测试通过！程序可以正常使用。")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
