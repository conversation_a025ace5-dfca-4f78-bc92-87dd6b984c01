import argparse
import os
import re
import sys
from typing import List, Optional, Tuple

import pandas as pd
from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet


def sanitize_filename(name: str, replacement: str = "_") -> str:
    name = str(name).strip()
    # Replace invalid filename characters on Windows
    name = re.sub(r'[\\/:*?"<>|]', replacement, name)
    # Collapse whitespace
    name = re.sub(r"\s+", " ", name).strip()
    # Avoid empty filenames
    return name if name else "未命名"


def read_template_header(template_csv_path: str, encoding: str = "utf-8") -> List[str]:
    with open(template_csv_path, "r", encoding=encoding, newline="") as f:
        first_line = f.readline().strip("\n\r")
        # Split by comma; allow simple CSV (no quoted commas handling needed for header)
        header = [h.strip() for h in first_line.split(",")]
    # Remove BOM if present
    if header and header[0].startswith('\ufeff'):
        header[0] = header[0][1:]
    return header


def _clean_cell_value(value):
    """清理单元格值：替换换行符为分号，去除首尾空白"""
    if value is None:
        return None
    s = str(value)
    # 替换各种换行符为分号
    s = s.replace('\n', ';').replace('\r\n', ';').replace('\r', ';')
    # 清理连续分号
    import re
    s = re.sub(r';+', ';', s)
    # 去除首尾分号和空白
    s = s.strip(';').strip()
    return s if s else None


def _merged_top_left_value(ws: Worksheet, row: int, col: int):
    # If (row, col) inside any merged range, return top-left value; else return cell value
    cell = ws.cell(row=row, column=col)
    for rng in ws.merged_cells.ranges:
        if (rng.min_row <= row <= rng.max_row) and (rng.min_col <= col <= rng.max_col):
            value = ws.cell(row=rng.min_row, column=rng.min_col).value
            return _clean_cell_value(value)
    return _clean_cell_value(cell.value)


def _extract_multi_cell_value(ws: Worksheet, row: int, col: int, max_col: int):
    """
    智能提取多单元格值的函数
    1. 先判断是否为合并单元格，如果是则直接返回合并单元格的值
    2. 如果不是合并单元格，则从当前列开始向右提取相关单元格值（包括空值）
    3. 智能判断字段边界，避免提取过多数据
    4. 用逗号+空格连接所有值，空值用空字符串表示
    """
    # 首先检查是否为合并单元格
    for rng in ws.merged_cells.ranges:
        if (rng.min_row <= row <= rng.max_row) and (rng.min_col <= col <= rng.max_col):
            # 是合并单元格，直接返回左上角的值
            value = ws.cell(row=rng.min_row, column=rng.min_col).value
            return _clean_cell_value(value)

    # 不是合并单元格，需要智能判断扫描范围
    values = []
    current_col = col
    max_scan_cells = 6  # 最多扫描6个单元格（通常一个复合字段不会超过这个数量）

    # 向右扫描，收集相关单元格的值
    for i in range(max_scan_cells):
        if current_col > max_col:
            break

        cell_value = ws.cell(row=row, column=current_col).value

        # 检查当前单元格是否是其他合并单元格的一部分
        is_other_merged = False
        for rng in ws.merged_cells.ranges:
            if (rng.min_row <= row <= rng.max_row) and (rng.min_col <= current_col <= rng.max_col):
                # 如果是其他合并单元格的一部分（不是起始列），停止扫描
                if rng.min_col != col:
                    is_other_merged = True
                    break

        if is_other_merged:
            break

        # 处理单元格值
        if cell_value is None or str(cell_value).strip() == "":
            # 空单元格，添加空字符串
            values.append("")
        else:
            # 非空单元格，清理值并添加
            cleaned_value = _clean_cell_value(cell_value)
            values.append(cleaned_value if cleaned_value else "")

        current_col += 1

    # 智能修剪结果：
    # 1. 移除末尾的连续空值
    while values and values[-1] == "":
        values.pop()

    # 2. 如果结果太长（超过预期的复合字段长度），进行智能截断
    if len(values) > 6:
        # 寻找合理的截断点：通常在单位后面
        truncate_at = 6
        for i in range(min(6, len(values))):
            if values[i] and any(unit in values[i].lower() for unit in ['m.h-1', 'm/h', 'h/', 'm/', 'kn', 'mpa', 'l/s']):
                truncate_at = i + 1
                break
        values = values[:truncate_at]

    # 3. 如果只有空值，返回None
    if not values or all(v == "" for v in values):
        return None

    # 用逗号+空格连接所有值
    return ", ".join(values)


def _replace_images_with_text(ws: Worksheet, placeholder: str = "图片") -> int:
    count = 0
    # Method 1: Try _images attribute
    images = getattr(ws, "_images", [])
    for img in images:
        anchor = getattr(img, "anchor", None)
        if anchor is None:
            continue
        # Try to get top-left cell (0-based) then convert to 1-based
        row = None
        col = None
        # openpyxl stores _from for OneCellAnchor/TwoCellAnchor
        from_anchor = getattr(anchor, "_from", None) or getattr(anchor, "from", None)
        if from_anchor is not None:
            row = getattr(from_anchor, "row", None)
            col = getattr(from_anchor, "col", None)
            if row is not None and col is not None:
                r = int(row) + 1
                c = int(col) + 1
                try:
                    ws.cell(row=r, column=c).value = placeholder
                    count += 1
                    continue
                except Exception:
                    pass
        # Fallback: if anchor is a cell address string like 'A1'
        if isinstance(anchor, str):
            try:
                from openpyxl.utils.cell import coordinate_to_tuple
                r, c = coordinate_to_tuple(anchor)
                ws.cell(row=r, column=c).value = placeholder
                count += 1
                continue
            except Exception:
                pass

    # Method 2: Try drawing parts (more comprehensive)
    try:
        if hasattr(ws, '_drawing') and ws._drawing is not None:
            for drawing in ws._drawing:
                if hasattr(drawing, 'anchor'):
                    anchor = drawing.anchor
                    from_anchor = getattr(anchor, '_from', None) or getattr(anchor, 'from', None)
                    if from_anchor is not None:
                        row = getattr(from_anchor, 'row', None)
                        col = getattr(from_anchor, 'col', None)
                        if row is not None and col is not None:
                            r = int(row) + 1
                            c = int(col) + 1
                            try:
                                ws.cell(row=r, column=c).value = placeholder
                                count += 1
                            except Exception:
                                pass
    except Exception:
        pass

    return count


def detect_header_row(ws: Worksheet, search_keywords: List[str], max_scan_rows: int = 20) -> int:
    max_r = min(ws.max_row or 1, max_scan_rows)
    for r in range(1, max_r + 1):
        values = [str((ws.cell(row=r, column=c).value or "")).strip() for c in range(1, (ws.max_column or 1) + 1)]
        for kw in search_keywords:
            if kw in values:
                return r
    return 1  # default


def sheet_to_dataframe(ws: Worksheet, header_row: Optional[int] = None) -> Tuple[pd.DataFrame, int, int]:
    # Replace images in-place
    _ = _replace_images_with_text(ws, placeholder="图片")

    # Auto-detect header row if not provided (seek '井号' first, else '日期')
    if header_row is None:
        header_row = detect_header_row(ws, ["井号", "日期"]) or 1

    max_row = ws.max_row or 0
    max_col = ws.max_column or 0

    # Extract header
    headers: List[str] = []
    for c in range(1, max_col + 1):
        v = _merged_top_left_value(ws, header_row, c)
        headers.append(str(v).strip() if v is not None else "")

    # Build data rows
    data: List[List[object]] = []
    for r in range(header_row + 1, max_row + 1):
        row_vals: List[object] = []
        empty = True
        for c in range(1, max_col + 1):
            v = _merged_top_left_value(ws, r, c)
            if v not in (None, ""):
                empty = False
            row_vals.append(v)
        if not empty:
            data.append(row_vals)

    # Create DataFrame
    df = pd.DataFrame(data, columns=headers)
    # Drop entirely empty columns (headers that are empty)
    if "" in df.columns:
        df = df.drop(columns=[col for col in df.columns if not str(col).strip()])

    # Trim header whitespace
    df.columns = [str(c).strip() for c in df.columns]

    return df, max_row, max_col


def normalize_label(s: object) -> str:
    if s is None:
        return ""
    t = str(s)
    # unify common punctuation and whitespace
    t = t.strip()
    t = t.replace("，", ",").replace("／", "/").replace("_", "/").replace(" ", "")
    t = t.replace("（", "(").replace("）", ")")
    # Handle specific field variations
    t = t.replace("各开实钻周期/模板周期", "各开次实钻周期/模板")
    t = t.replace("各开实钻周期", "各开次实钻周期")
    # Don't modify photo field names - keep them as is for exact matching
    return t


def try_extract_crosstab(ws: Worksheet, template_header: List[str], well_col_name: str = "井号") -> pd.DataFrame:
    """
    尝试按“交叉表”结构解析：
    - 假设第一列是字段名（应与模板列名对应，除‘日期’外）
    - 井号位于某一行的第1列为“井号”，该行的第2..N列为井名
    - 单元格内的图片已在调用前替换为“图片”
    返回 DataFrame（包含列：井号、日期、以及模板中的字段列）。
    若无法判定为交叉表，返回空 DataFrame。
    """
    _ = _replace_images_with_text(ws, placeholder="图片")

    max_row = ws.max_row or 0
    max_col = ws.max_column or 0
    if max_row == 0 or max_col == 0:
        return pd.DataFrame()

    # 1) 寻找“井号”所在的行（A列）作为井名表头行
    header_row_candidates = []
    for r in range(1, min(max_row, 10) + 1):
        v = ws.cell(row=r, column=1).value
        if str(v).strip() in ("井号", "井名", "井位", "井别"):
            header_row_candidates.append(r)
    well_header_row = header_row_candidates[0] if header_row_candidates else None

    # 2) 若未找到，尝试通过模板字段定位：第一列若包含大量模板字段，则认为其下方为字段区，上方一行为井名表头
    template_fields = [h for h in template_header if h and h != "日期"]
    tmpl_norm_map = {normalize_label(h): h for h in template_fields}

    if well_header_row is None:
        overlap_counts = []
        for r in range(1, min(max_row, 10) + 1):
            # 统计从该行开始的第一列与模板字段的重合数量
            labels = [normalize_label(ws.cell(row=rr, column=1).value) for rr in range(r, max_row + 1)]
            overlap = sum(1 for lab in labels if lab in tmpl_norm_map)
            overlap_counts.append((r, overlap))
        # 选择重合最多且r>1的情形，认为r为字段起始行，r-1为井名表头行
        if overlap_counts:
            r_best, best = max(overlap_counts, key=lambda x: x[1])
            if best >= 3 and r_best > 1:
                well_header_row = r_best - 1

    if well_header_row is None:
        return pd.DataFrame()

    # 3) 读取井名（该行2..N列），处理空格和空列
    well_names = []
    well_cols = []
    for c in range(2, max_col + 1):
        name = ws.cell(row=well_header_row, column=c).value
        if name is None:
            continue
        name_str = str(name).strip()
        if name_str == "":
            continue
        well_names.append(name_str)
        well_cols.append(c)
    if not well_cols:
        return pd.DataFrame()

    # 4) 字段从 well_header_row+1 开始，第一列为字段名
    start_row = well_header_row + 1
    records = []
    for c, wname in zip(well_cols, well_names):
        rec = {well_col_name: str(wname).strip(), "日期": ws.title}
        # 遍历字段行
        for r in range(start_row, max_row + 1):
            raw_label = ws.cell(row=r, column=1).value
            norm_label = normalize_label(raw_label)
            if norm_label in tmpl_norm_map:
                col_name = tmpl_norm_map[norm_label]
                # 不要覆盖日期列，保持使用工作表名称
                if col_name != "日期":
                    # 使用新的多单元格值提取函数
                    val = _extract_multi_cell_value(ws, r, c, max_col)
                    rec[col_name] = val
            # 处理子字段（如照片字段）
            elif raw_label is None:
                # 检查当前行的值是否是子字段名
                sub_field = _merged_top_left_value(ws, r, c)
                if sub_field and isinstance(sub_field, str):
                    sub_field_norm = normalize_label(sub_field)
                    if sub_field_norm in tmpl_norm_map:
                        col_name = tmpl_norm_map[sub_field_norm]
                        if col_name != "日期":
                            # 子字段的值在下一行
                            if r + 1 <= max_row:
                                val = _extract_multi_cell_value(ws, r + 1, c, max_col)
                                rec[col_name] = val
        records.append(rec)

    df = pd.DataFrame.from_records(records)
    return df


def main():
    parser = argparse.ArgumentParser(description="按井号从Excel抽取并生成单井CSV（UTF-8），图片替换为‘图片’文本")
    parser.add_argument("--input", required=True, help="输入xlsx文件路径")
    parser.add_argument("--output-dir", default="输出", help="输出目录（不存在将创建）")
    parser.add_argument("--template", default="统计结果模板.csv", help="统计结果模板CSV路径（首行为表头）")
    parser.add_argument("--well-col", default="井号", help="井名列名（默认：井号）")
    parser.add_argument("--header-row", type=int, default=None, help="指定工作表表头所在行（1-based），不指定则自动探测")
    parser.add_argument("--encoding", default="utf-8", choices=["utf-8", "utf-8-sig"], help="导出CSV编码")
    parser.add_argument("--sheets", nargs="*", default=None, help="仅处理指定工作表名（留空为全部）")

    args = parser.parse_args()

    input_path = args.input
    output_dir = args.output_dir
    template_path = args.template
    well_col = args.well_col.strip()
    header_row_opt = args.header_row
    encoding = args.encoding
    only_sheets = set(args.sheets) if args.sheets else None

    if not os.path.exists(input_path):
        print(f"输入文件不存在: {input_path}", file=sys.stderr)
        sys.exit(1)

    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}", file=sys.stderr)
        sys.exit(1)

    os.makedirs(output_dir, exist_ok=True)

    # Read template header
    try:
        template_header = read_template_header(template_path, encoding="utf-8")
        template_header = [h for h in template_header if h]  # remove empty
    except Exception as e:
        print(f"读取模板表头失败: {e}", file=sys.stderr)
        sys.exit(1)

    # Load workbook
    try:
        wb = load_workbook(input_path, data_only=True)
    except Exception as e:
        print(f"读取Excel失败: {e}", file=sys.stderr)
        sys.exit(1)

    all_dfs: List[pd.DataFrame] = []
    sheet_stats = []

    for ws in wb.worksheets:
        if only_sheets and ws.title not in only_sheets:
            continue
        try:
            # 优先尝试交叉表模式
            crosstab_df = try_extract_crosstab(ws, template_header, well_col_name=well_col)
            if crosstab_df is not None and not crosstab_df.empty:
                df = crosstab_df
                max_r, max_c = ws.max_row or 0, ws.max_column or 0
            else:
                # 回退为行式表头模式
                df, max_r, max_c = sheet_to_dataframe(ws, header_row=header_row_opt)

            if df.empty:
                sheet_stats.append((ws.title, 0, max_r, max_c))
                continue
            # Add a column to trace sheet name (optional debugging)
            df["来源工作表"] = ws.title
            all_dfs.append(df)
            sheet_stats.append((ws.title, len(df), max_r, max_c))
        except Exception as e:
            print(f"工作表[{ws.title}]处理失败: {e}", file=sys.stderr)

    if not all_dfs:
        print("未从任何工作表读取到有效数据。", file=sys.stderr)
        sys.exit(2)

    combined = pd.concat(all_dfs, ignore_index=True, sort=False)

    # Normalize column names (strip)
    combined.columns = [str(c).strip() for c in combined.columns]

    # Ensure well column exists
    if well_col not in combined.columns:
        # Try some common variants
        fallback_cols = ["井名", "井号", "井位", "井别"]
        found = None
        for c in fallback_cols:
            if c in combined.columns:
                found = c
                break
        if found is None:
            print(f"找不到井名列 '{well_col}'（且未发现常见备选列）。可通过 --well-col 指定。", file=sys.stderr)
            # Show available columns
            print("可用列:", ", ".join(combined.columns), file=sys.stderr)
            sys.exit(3)
        else:
            well_col = found

    # Drop rows with empty well id
    combined[well_col] = combined[well_col].astype(str).str.strip()
    combined = combined[combined[well_col] != ""]

    # Reorder/select columns according to template
    # Keep only columns listed in template header; add missing columns as empty
    for col in template_header:
        if col not in combined.columns:
            combined[col] = None
    ordered_cols = [c for c in template_header if c in combined.columns]

    # Group by well and export
    well_groups = combined.groupby(well_col, dropna=True)
    exported = 0
    for well_name, gdf in well_groups:
        if not isinstance(well_name, str):
            well_name = str(well_name)
        fname = f"{sanitize_filename(well_name)}-钻井日报.csv"
        out_path = os.path.join(output_dir, fname)
        # Subset and order columns
        out_df = gdf[ordered_cols].copy()
        # Optional sort by 日期 if exists
        if "日期" in out_df.columns:
            try:
                out_df_sorted = out_df.copy()
                out_df_sorted["__日期排序__"] = pd.to_datetime(out_df_sorted["日期"], errors="coerce")
                out_df = out_df_sorted.sort_values("__日期排序__").drop(columns=["__日期排序__"])
            except Exception:
                pass
        # Export CSV
        out_df.to_csv(out_path, index=False, encoding=encoding)
        exported += 1
        print(f"导出: {out_path} ({len(out_df)} 行)")

    # Summary
    print("处理完成。")
    for title, rows, max_r, max_c in sheet_stats:
        print(f"- 工作表: {title}，读到 {rows} 行（原始尺寸 R{max_r}xC{max_c}）")
    print(f"共导出 {exported} 口井的数据。")


if __name__ == "__main__":
    main()

