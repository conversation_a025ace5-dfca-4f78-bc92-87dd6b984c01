#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel照片提取器
从Excel文件中提取图片并保存为PNG格式
命名规则：表头+指标（入井/出井/下趟）+日期（YYYY-MM-DD）
"""

import os
import re
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import pandas as pd
from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet
from PIL import Image
import io


class ExcelPhotoExtractor:
    def __init__(self):
        self.year = None
        self.output_dir = "output"
        self.supported_formats = ['.xlsx', '.xlsm']
        
    def sanitize_filename(self, name: str, replacement: str = "_") -> str:
        """清理文件名，移除非法字符"""
        name = str(name).strip()
        # 替换Windows文件名非法字符
        name = re.sub(r'[\\/:*?"<>|]', replacement, name)
        # 合并空白字符
        name = re.sub(r"\s+", " ", name).strip()
        # 避免空文件名
        return name if name else "未命名"
    
    def detect_header_row(self, ws: Worksheet, search_keywords: List[str], max_scan_rows: int = 20) -> int:
        """检测表头行位置"""
        max_r = min(ws.max_row or 1, max_scan_rows)
        for r in range(1, max_r + 1):
            values = [str((ws.cell(row=r, column=c).value or "")).strip() 
                     for c in range(1, (ws.max_column or 1) + 1)]
            for kw in search_keywords:
                if kw in values:
                    return r
        return 1  # 默认第一行
    
    def get_merged_cell_value(self, ws: Worksheet, row: int, col: int):
        """获取合并单元格的值"""
        cell = ws.cell(row=row, column=col)
        for rng in ws.merged_cells.ranges:
            if (rng.min_row <= row <= rng.max_row) and (rng.min_col <= col <= rng.max_col):
                value = ws.cell(row=rng.min_row, column=rng.min_col).value
                return str(value).strip() if value is not None else ""
        return str(cell.value).strip() if cell.value is not None else ""
    
    def extract_images_from_worksheet(self, ws: Worksheet, year: str) -> List[Dict]:
        """从工作表中提取图片信息"""
        images_info = []

        # 检测表头行
        header_row = self.detect_header_row(ws, ["井号", "日期", "时间"])

        # 获取表头信息
        headers = {}
        max_col = ws.max_column or 0
        for c in range(1, max_col + 1):
            header_value = self.get_merged_cell_value(ws, header_row, c)
            if header_value:
                headers[c] = header_value

        # 方法1: 尝试从_images属性提取
        if hasattr(ws, '_images') and ws._images:
            for img in ws._images:
                try:
                    img_info = self._process_single_image(img, ws, headers, header_row, year)
                    if img_info:
                        images_info.append(img_info)
                except Exception as e:
                    print(f"处理图片时出错: {e}")
                    continue

        # 方法2: 尝试从drawing对象提取
        if hasattr(ws, '_drawing') and ws._drawing:
            for drawing in ws._drawing:
                try:
                    if hasattr(drawing, 'pic') and hasattr(drawing.pic, 'blipFill'):
                        img_info = self._process_drawing_image(drawing, ws, headers, header_row, year)
                        if img_info:
                            images_info.append(img_info)
                except Exception as e:
                    print(f"处理drawing图片时出错: {e}")
                    continue

        return images_info

    def _process_single_image(self, img, ws: Worksheet, headers: Dict, header_row: int, year: str) -> Optional[Dict]:
        """处理单个图片对象"""
        # 获取图片位置信息
        anchor = getattr(img, 'anchor', None)
        if anchor is None:
            return None

        # 获取图片所在的单元格位置
        row, col = self._get_image_position(anchor)
        if row is None or col is None:
            return None

        # 获取图片数据
        img_data = self._get_image_data(img)
        if img_data is None:
            return None

        # 分析图片所在位置的上下文信息
        context_info = self._analyze_image_context(ws, row, col, headers, header_row)

        # 生成文件名
        filename = self._generate_filename(context_info, year, ws.title)

        return {
            'data': img_data,
            'filename': filename,
            'row': row,
            'col': col,
            'context': context_info
        }

    def _process_drawing_image(self, drawing, ws: Worksheet, headers: Dict, header_row: int, year: str) -> Optional[Dict]:
        """处理drawing对象中的图片"""
        try:
            # 获取位置信息
            anchor = getattr(drawing, 'anchor', None)
            if anchor is None:
                return None

            row, col = self._get_image_position(anchor)
            if row is None or col is None:
                return None

            # 尝试获取图片数据
            img_data = None
            if hasattr(drawing, 'pic') and hasattr(drawing.pic, 'blipFill'):
                blip = drawing.pic.blipFill.blip
                if hasattr(blip, 'embed'):
                    # 从工作簿的关系中获取图片数据
                    img_data = self._get_embedded_image_data(ws, blip.embed)

            if img_data is None:
                return None

            # 分析上下文信息
            context_info = self._analyze_image_context(ws, row, col, headers, header_row)

            # 生成文件名
            filename = self._generate_filename(context_info, year, ws.title)

            return {
                'data': img_data,
                'filename': filename,
                'row': row,
                'col': col,
                'context': context_info
            }

        except Exception as e:
            print(f"处理drawing图片时出错: {e}")
            return None

    def _get_embedded_image_data(self, ws: Worksheet, embed_id: str) -> Optional[bytes]:
        """从工作簿中获取嵌入的图片数据"""
        try:
            # 获取工作簿对象
            wb = ws.parent
            if hasattr(wb, '_images'):
                for img_id, img_data in wb._images.items():
                    if img_id == embed_id:
                        return img_data

            # 尝试从关系中获取
            if hasattr(ws, '_rels'):
                for rel in ws._rels.values():
                    if rel.id == embed_id and hasattr(rel, 'target_part'):
                        return rel.target_part._blob

        except Exception:
            pass
        return None
    
    def _get_image_position(self, anchor) -> Tuple[Optional[int], Optional[int]]:
        """获取图片在工作表中的位置"""
        try:
            # 尝试获取锚点位置
            from_anchor = getattr(anchor, '_from', None) or getattr(anchor, 'from', None)
            if from_anchor is not None:
                row = getattr(from_anchor, 'row', None)
                col = getattr(from_anchor, 'col', None)
                if row is not None and col is not None:
                    return int(row) + 1, int(col) + 1  # 转换为1基索引
            
            # 备用方法：如果anchor是字符串地址
            if isinstance(anchor, str):
                from openpyxl.utils.cell import coordinate_to_tuple
                row, col = coordinate_to_tuple(anchor)
                return row, col
                
        except Exception:
            pass
        
        return None, None
    
    def _get_image_data(self, img) -> Optional[bytes]:
        """获取图片的二进制数据"""
        try:
            # 方法1: 直接从_data方法获取
            if hasattr(img, '_data') and callable(img._data):
                data = img._data()
                if data:
                    return data

            # 方法2: 从ref属性获取
            if hasattr(img, 'ref') and img.ref:
                return img.ref

            # 方法3: 从_image属性获取
            if hasattr(img, '_image') and img._image:
                return img._image

            # 方法4: 如果是PIL Image对象，转换为bytes
            if hasattr(img, 'image'):
                pil_img = img.image
                if hasattr(pil_img, 'save'):
                    img_bytes = io.BytesIO()
                    pil_img.save(img_bytes, format='PNG')
                    return img_bytes.getvalue()

            # 方法5: 尝试从path属性读取文件
            if hasattr(img, 'path') and img.path:
                try:
                    with open(img.path, 'rb') as f:
                        return f.read()
                except Exception:
                    pass

        except Exception as e:
            print(f"获取图片数据时出错: {e}")
        return None
    
    def _analyze_image_context(self, ws: Worksheet, img_row: int, img_col: int, 
                              headers: Dict, header_row: int) -> Dict:
        """分析图片所在位置的上下文信息"""
        context = {
            'header': '',
            'indicator': '',
            'date': '',
            'well_name': ''
        }
        
        # 获取对应的表头
        if img_col in headers:
            context['header'] = headers[img_col]
        
        # 分析指标类型（入井、出井、下趟）
        # 首先检查对应的表头信息
        if img_col in headers:
            header_text = headers[img_col].lower()
            if any(keyword in header_text for keyword in ['入井', '下井', '开钻']):
                context['indicator'] = '入井'
            elif any(keyword in header_text for keyword in ['出井', '起钻', '完井']):
                context['indicator'] = '出井'
            elif any(keyword in header_text for keyword in ['下趟', '下钻具', '下套管']):
                context['indicator'] = '下趟'

        # 如果表头没有明确指标，检查图片周围的文本内容
        if not context['indicator']:
            for check_row in range(max(1, img_row - 2), min(ws.max_row + 1, img_row + 3)):
                for check_col in range(max(1, img_col - 2), min(ws.max_column + 1, img_col + 3)):
                    cell_value = self.get_merged_cell_value(ws, check_row, check_col)
                    if cell_value:
                        cell_text = cell_value.lower()
                        if any(keyword in cell_text for keyword in ['入井', '下井', '开钻']):
                            context['indicator'] = '入井'
                            break
                        elif any(keyword in cell_text for keyword in ['出井', '起钻', '完井']):
                            context['indicator'] = '出井'
                            break
                        elif any(keyword in cell_text for keyword in ['下趟', '下钻具', '下套管']):
                            context['indicator'] = '下趟'
                            break
                if context['indicator']:
                    break
        
        # 如果没有找到明确的指标，使用默认值
        if not context['indicator']:
            context['indicator'] = '作业'
        
        # 尝试获取日期信息
        context['date'] = self._extract_date_from_context(ws, img_row, img_col, header_row)
        
        # 尝试获取井名
        context['well_name'] = self._extract_well_name(ws, img_row, headers, header_row)
        
        return context
    
    def _extract_date_from_context(self, ws: Worksheet, img_row: int, img_col: int, header_row: int) -> str:
        """从上下文中提取日期信息"""
        # 首先尝试从工作表名称中提取日期
        sheet_title = ws.title
        date_patterns = [
            r'(\d{1,2})[月-](\d{1,2})[日]?',  # 5月5日 或 5-5
            r'(\d{1,2})/(\d{1,2})',          # 5/5
            r'(\d{1,2})\.(\d{1,2})',         # 5.5
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, sheet_title)
            if match:
                month, day = match.groups()
                return f"{month.zfill(2)}-{day.zfill(2)}"
        
        # 尝试从图片附近的单元格中查找日期
        for check_row in range(max(header_row, img_row - 3), min(ws.max_row + 1, img_row + 3)):
            for check_col in range(max(1, img_col - 3), min(ws.max_column + 1, img_col + 3)):
                cell_value = self.get_merged_cell_value(ws, check_row, check_col)
                if cell_value:
                    for pattern in date_patterns:
                        match = re.search(pattern, cell_value)
                        if match:
                            month, day = match.groups()
                            return f"{month.zfill(2)}-{day.zfill(2)}"
        
        return "01-01"  # 默认日期
    
    def _extract_well_name(self, ws: Worksheet, img_row: int, headers: Dict, header_row: int) -> str:
        """提取井名信息"""
        # 查找井号列
        well_col = None
        for col, header in headers.items():
            if any(keyword in header for keyword in ['井号', '井名', '井位']):
                well_col = col
                break
        
        if well_col:
            # 从图片所在行或附近行获取井名
            for check_row in range(max(header_row + 1, img_row - 2), min(ws.max_row + 1, img_row + 3)):
                well_name = self.get_merged_cell_value(ws, check_row, well_col)
                if well_name and well_name not in ['井号', '井名', '井位']:
                    return well_name
        
        return "未知井"
    
    def _generate_filename(self, context: Dict, year: str, sheet_name: str) -> str:
        """生成文件名"""
        # 构建文件名：表头+指标+日期
        parts = []
        
        # 添加表头信息
        if context['header']:
            parts.append(self.sanitize_filename(context['header']))
        elif context['well_name'] and context['well_name'] != "未知井":
            parts.append(self.sanitize_filename(context['well_name']))
        else:
            parts.append(self.sanitize_filename(sheet_name))
        
        # 添加指标
        if context['indicator']:
            parts.append(context['indicator'])
        
        # 添加日期
        if context['date'] and context['date'] != "01-01":
            date_str = f"{year}-{context['date']}"
        else:
            date_str = f"{year}-01-01"
        
        parts.append(date_str)
        
        # 组合文件名
        filename = "_".join(parts) + ".png"
        return filename

    def save_image_as_png(self, img_data: bytes, filepath: str) -> bool:
        """将图片数据保存为PNG文件"""
        try:
            # 使用PIL打开图片数据
            image = Image.open(io.BytesIO(img_data))

            # 转换为RGB模式（PNG支持）
            if image.mode in ('RGBA', 'LA', 'P'):
                # 保持透明度
                image = image.convert('RGBA')
            else:
                image = image.convert('RGB')

            # 保存为PNG
            image.save(filepath, 'PNG', optimize=True)
            return True

        except Exception as e:
            print(f"保存图片失败 {filepath}: {e}")
            return False

    def process_excel_file(self, excel_path: str, year: str) -> int:
        """处理单个Excel文件"""
        try:
            print(f"正在处理: {excel_path}")
            wb = load_workbook(excel_path, data_only=True)
            total_images = 0

            for ws in wb.worksheets:
                print(f"  处理工作表: {ws.title}")
                images_info = self.extract_images_from_worksheet(ws, year)

                for img_info in images_info:
                    # 确保输出目录存在
                    os.makedirs(self.output_dir, exist_ok=True)

                    # 生成完整的文件路径
                    filepath = os.path.join(self.output_dir, img_info['filename'])

                    # 如果文件已存在，添加序号
                    counter = 1
                    original_filepath = filepath
                    while os.path.exists(filepath):
                        name, ext = os.path.splitext(original_filepath)
                        filepath = f"{name}_{counter}{ext}"
                        counter += 1

                    # 保存图片
                    if self.save_image_as_png(img_info['data'], filepath):
                        print(f"    保存图片: {os.path.basename(filepath)}")
                        total_images += 1
                    else:
                        print(f"    保存失败: {os.path.basename(filepath)}")

            wb.close()
            return total_images

        except Exception as e:
            print(f"处理Excel文件失败 {excel_path}: {e}")
            return 0

    def process_folder(self, folder_path: str, year: str) -> Tuple[int, int]:
        """处理文件夹中的所有Excel文件"""
        total_files = 0
        total_images = 0

        # 遍历文件夹中的所有文件
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)

            # 检查是否为Excel文件
            if os.path.isfile(file_path) and any(filename.lower().endswith(ext) for ext in self.supported_formats):
                total_files += 1
                images_count = self.process_excel_file(file_path, year)
                total_images += images_count

        return total_files, total_images


class PhotoExtractorGUI:
    def __init__(self):
        self.extractor = ExcelPhotoExtractor()
        self.setup_gui()

    def setup_gui(self):
        """设置图形用户界面"""
        self.root = tk.Tk()
        self.root.title("Excel照片提取器")
        self.root.geometry("600x400")
        self.root.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 年份设置
        ttk.Label(main_frame, text="年份设置:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        year_frame = ttk.Frame(main_frame)
        year_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Entry(year_frame, textvariable=self.year_var, width=10).pack(side=tk.LEFT)
        ttk.Label(year_frame, text="(格式: 2024)").pack(side=tk.LEFT, padx=(10, 0))

        # 文件夹选择
        ttk.Label(main_frame, text="选择文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)
        folder_frame = ttk.Frame(main_frame)
        folder_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        folder_frame.columnconfigure(0, weight=1)

        self.folder_var = tk.StringVar()
        ttk.Entry(folder_frame, textvariable=self.folder_var, state='readonly').grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).grid(row=0, column=1, padx=(5, 0))

        # 输出目录设置
        ttk.Label(main_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        output_frame.columnconfigure(0, weight=1)

        self.output_var = tk.StringVar(value="output")
        ttk.Entry(output_frame, textvariable=self.output_var).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(output_frame, text="浏览", command=self.browse_output).grid(row=0, column=1, padx=(5, 0))

        # 处理按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)

        self.process_btn = ttk.Button(button_frame, text="开始提取照片", command=self.start_processing)
        self.process_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=5)

        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=4, column=0, columnspan=2, pady=10)

        # 日志文本框
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)

        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

    def browse_folder(self):
        """浏览选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含Excel文件的文件夹")
        if folder:
            self.folder_var.set(folder)

    def browse_output(self):
        """浏览选择输出目录"""
        folder = filedialog.askdirectory(title="选择输出目录")
        if folder:
            self.output_var.set(folder)

    def log_message(self, message: str):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def start_processing(self):
        """开始处理"""
        # 验证输入
        year = self.year_var.get().strip()
        folder = self.folder_var.get().strip()
        output_dir = self.output_var.get().strip()

        if not year or not year.isdigit() or len(year) != 4:
            messagebox.showerror("错误", "请输入有效的年份（4位数字）")
            return

        if not folder or not os.path.exists(folder):
            messagebox.showerror("错误", "请选择有效的文件夹")
            return

        if not output_dir:
            messagebox.showerror("错误", "请设置输出目录")
            return

        # 设置提取器参数
        self.extractor.output_dir = output_dir

        # 禁用处理按钮
        self.process_btn.config(state='disabled')

        try:
            self.progress_var.set("正在处理...")
            self.log_message("开始处理Excel文件...")
            self.log_message(f"年份: {year}")
            self.log_message(f"输入文件夹: {folder}")
            self.log_message(f"输出目录: {output_dir}")

            # 处理文件夹
            total_files, total_images = self.extractor.process_folder(folder, year)

            # 显示结果
            result_msg = f"处理完成！\n共处理 {total_files} 个Excel文件\n提取 {total_images} 张图片"
            self.log_message(result_msg)
            self.progress_var.set("处理完成")

            messagebox.showinfo("完成", result_msg)

        except Exception as e:
            error_msg = f"处理过程中出现错误: {e}"
            self.log_message(error_msg)
            self.progress_var.set("处理失败")
            messagebox.showerror("错误", error_msg)

        finally:
            # 重新启用处理按钮
            self.process_btn.config(state='normal')

    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    # 检查必要的依赖
    try:
        import openpyxl
        import PIL
        import pandas as pd
    except ImportError as e:
        print(f"缺少必要的依赖库: {e}")
        print("请安装: pip install openpyxl pillow pandas")
        sys.exit(1)

    # 启动GUI
    app = PhotoExtractorGUI()
    app.run()


if __name__ == "__main__":
    main()
