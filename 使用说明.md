# Excel照片提取器使用说明

## 快速开始

### 1. 双击运行
直接双击 `run.bat` 文件即可启动程序。脚本会自动检查Python环境和依赖库，如有缺失会自动安装。

### 2. 手动运行
如果您熟悉Python，也可以直接运行：
```bash
python excel_photo_extractor.py
```

## 程序界面说明

### 主要设置项

1. **年份设置**
   - 输入4位数字的年份（如：2024）
   - 这个年份会用于生成图片文件名中的日期部分
   - 因为Excel表格中通常只有月日信息，缺少年份

2. **选择文件夹**
   - 点击"浏览"按钮选择包含Excel文件的文件夹
   - 程序会自动处理该文件夹中所有的.xlsx和.xlsm文件
   - 支持子文件夹中的Excel文件

3. **输出目录**
   - 设置提取的图片保存位置
   - 默认为"output"文件夹
   - 如果目录不存在，程序会自动创建

### 操作步骤

1. 设置年份（如：2024）
2. 选择包含Excel文件的文件夹
3. 确认输出目录设置
4. 点击"开始提取照片"按钮
5. 在日志区域查看处理进度
6. 处理完成后会显示结果统计

## 文件命名规则

提取的图片会按照以下规则命名：
```
表头_指标_日期.png
```

### 示例
- `钻井参数_入井_2024-05-15.png`
- `设备状态_出井_2024-05-16.png`
- `作业记录_下趟_2024-05-17.png`

### 命名规则详解

1. **表头部分**：从Excel表格的列标题中提取
2. **指标部分**：程序会自动识别以下类型
   - `入井`：包含"入井"、"下井"、"开钻"等关键词
   - `出井`：包含"出井"、"起钻"、"完井"等关键词
   - `下趟`：包含"下趟"、"下钻具"、"下套管"等关键词
   - `作业`：无法识别具体类型时的默认值
3. **日期部分**：结合设置的年份和从Excel中提取的月日信息

## 支持的Excel格式

- `.xlsx` 文件（推荐）
- `.xlsm` 文件（包含宏的Excel文件）

## 注意事项

### Excel文件要求
1. 表格应有清晰的表头行，包含"井号"、"日期"等关键字段
2. 图片应嵌入在表格的单元格中
3. 表头应包含明确的指标信息（如"入井照片"、"出井照片"等）

### 文件名处理
1. 如果生成的文件名重复，程序会自动添加序号（如：`文件名_1.png`）
2. 文件名中的非法字符会被替换为下划线
3. 空白字符会被合并为单个空格

### 错误处理
1. 无法处理的图片会被跳过，并在日志中显示错误信息
2. 损坏的Excel文件会被跳过
3. 程序会继续处理其他文件，不会因单个文件错误而停止

## 常见问题

### Q: 程序提示"找不到图片"
**A:** 请检查：
- Excel文件中是否确实包含图片
- 图片是否正确嵌入在单元格中（而不是浮动在表格上方）
- Excel文件格式是否为.xlsx或.xlsm

### Q: 生成的文件名不正确
**A:** 请检查：
- Excel表格的表头是否包含明确的指标信息
- 工作表名称是否包含日期信息
- 年份设置是否正确

### Q: 程序运行缓慢
**A:** 这是正常现象，因为：
- 图片提取和转换需要时间
- 大文件处理会比较慢
- 可以在日志区域查看处理进度

### Q: 某些图片没有被提取
**A:** 可能的原因：
- 图片格式不支持
- 图片数据损坏
- 图片位置信息缺失
- 查看日志区域的错误信息获取详细原因

## 技术支持

如果遇到问题，请：
1. 查看程序界面中的"处理日志"区域
2. 检查错误信息和提示
3. 确认Excel文件格式和内容是否符合要求
4. 尝试用较小的测试文件验证程序功能

## 版本信息

- 当前版本：v1.0.0
- 支持的Python版本：3.7+
- 主要依赖：openpyxl, Pillow, pandas
