# Excel照片提取器

这是一个专门用于从Excel文件中提取图片并保存为PNG格式的工具。程序会根据图片在表格中的位置和上下文信息自动生成有意义的文件名。

## 功能特点

- **批量处理**: 可以选择文件夹，自动处理其中所有的Excel文件（.xlsx, .xlsm格式）
- **智能命名**: 根据表头+指标（入井/出井/下趟）+日期（YYYY-MM-DD）的规则自动命名
- **年份设置**: 用户可以设置年份，因为Excel表格中通常缺少年份信息
- **图形界面**: 提供友好的图形用户界面，操作简单
- **统一输出**: 所有提取的图片统一保存到output文件夹中

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖库
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install openpyxl pillow pandas
```

## 使用方法

### 1. 启动程序
```bash
python excel_photo_extractor.py
```

### 2. 设置参数
- **年份设置**: 输入4位数字的年份（如：2024）
- **选择文件夹**: 点击"浏览"按钮选择包含Excel文件的文件夹
- **输出目录**: 设置图片保存的目录（默认为"output"）

### 3. 开始处理
点击"开始提取照片"按钮，程序会：
1. 扫描选定文件夹中的所有Excel文件
2. 从每个文件的每个工作表中提取图片
3. 根据图片位置和上下文信息生成文件名
4. 将图片保存为PNG格式到输出目录

## 文件命名规则

程序会根据以下规则自动生成文件名：
```
表头_指标_日期.png
```

### 示例：
- `钻井参数_入井_2024-05-15.png`
- `设备状态_出井_2024-05-16.png`
- `作业记录_下趟_2024-05-17.png`

### 命名规则说明：
1. **表头**: 从Excel表格的列标题中提取
2. **指标**: 自动识别图片周围的文本，判断是"入井"、"出井"还是"下趟"
3. **日期**: 结合用户设置的年份和从工作表中提取的月日信息

## 支持的文件格式

### 输入格式
- Excel文件：.xlsx, .xlsm

### 输出格式
- 图片：PNG格式

## 注意事项

1. **Excel文件结构**: 程序假设Excel文件有标准的表头行，包含"井号"、"日期"等关键字段
2. **图片位置**: 程序会分析图片在表格中的位置，结合周围的文本信息来生成文件名
3. **重复文件名**: 如果生成的文件名重复，程序会自动添加序号（如：文件名_1.png）
4. **错误处理**: 程序会跳过无法处理的图片，并在日志中显示错误信息

## 故障排除

### 常见问题

1. **找不到图片**
   - 确保Excel文件中确实包含图片
   - 检查Excel文件格式是否支持（.xlsx, .xlsm）

2. **文件名异常**
   - 检查Excel表格的表头是否规范
   - 确保工作表名称包含日期信息

3. **程序崩溃**
   - 检查Python版本和依赖库是否正确安装
   - 查看错误日志获取详细信息

### 获取帮助

如果遇到问题，请检查程序界面中的"处理日志"区域，其中包含详细的处理信息和错误消息。

## 更新日志

### v1.0.0
- 初始版本
- 支持从Excel文件中提取图片
- 智能文件命名功能
- 图形用户界面
- 批量处理功能
